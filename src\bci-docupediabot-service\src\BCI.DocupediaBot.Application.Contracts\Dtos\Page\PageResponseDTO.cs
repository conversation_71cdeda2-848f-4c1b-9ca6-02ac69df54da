﻿using System;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Page
{
  public class PageResponseDTO
	{
		public Guid Id { get; set; }
		public Guid CollectionId { get; set; }
		public string Title { get; set; } = default!;
		public string Url { get; set; } = default!;
		public bool IsIncludeChild { get; set; } = true;
		public string SourceId { get; set; } = default!;
		public bool IsEmbedding { get; set; } = false;
		public int ContentNumber { get; set; } = default!;
		public int? VersionNo { get; set; } = default!;
		public int? EmbeddingVersionNo { get; set; } = default!;
		public DateTime? ModificationTime { get; set; }
		public string? JobId { get; set; }
	}
}
