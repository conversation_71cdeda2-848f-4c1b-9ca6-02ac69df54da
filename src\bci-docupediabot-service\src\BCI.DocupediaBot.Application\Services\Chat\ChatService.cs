﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Chat;
using BCI.DocupediaBot.Application.Services.History;
using BCI.DocupediaBot.Application.Services.LLM;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Application.Services.VectorDb;
using BCI.DocupediaBot.Infrastructure;
using BCI.DocupediaBot.Infrastructure.Configuration;
using BCI.DocupediaBot.Infrastructure.Constants;
using BCI.DocupediaBot.Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Chat
{
  public class ChatService : IChatService
	{
		private readonly IVectorDbService _vectorDbService;
		private readonly ILLMService _llmService;
		private readonly IPageService _pageService;
		private readonly ILogger<ChatService> _logger;
    private readonly IHistoryService _historyService;


    public ChatService(
        IVectorDbService vectorDbService,
        IHttpClientFactory httpClientFactory,
        ILogger<ChatService> logger,
        ILLMService llmService,
        IPageService pageService,
        IHistoryService historyService)
    {
      _vectorDbService = vectorDbService ?? throw new ArgumentNullException(nameof(vectorDbService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _llmService = llmService;
      _pageService = pageService;
      _historyService = historyService;
    }
    public async Task<string> AskQuestionAsync(ChatDTO dto)
    {
      var totalStopwatch = Stopwatch.StartNew(); // Total execution time
      var timings = new List<string>(); // Store individual timings

      if (string.IsNullOrWhiteSpace(dto.UserMessage))
      {
        _logger.LogWarning("User message is empty in AskQuestionAsync.");
        throw new ArgumentException(ErrorMessages.UserMessageEmpty);
      }

      try
      {
        ChatHistoryAddDTO historyDto;
        double handlerTime = 0;

        if (dto.UserMessage.Equals("Summarizing Collection", StringComparison.OrdinalIgnoreCase))
        {
          var handlerStopwatch = Stopwatch.StartNew();
          historyDto = await HandleSummarizeCollectionAsync(dto);
          handlerStopwatch.Stop();
          handlerTime = handlerStopwatch.ElapsedMilliseconds / 1000.0;
          timings.Add($"SummarizeCollection: {handlerTime:F1} seconds");
        }
        else if (dto.UserMessage.Equals("Listing recently updated documents", StringComparison.OrdinalIgnoreCase))
        {
          var handlerStopwatch = Stopwatch.StartNew();
          historyDto = await HandleRecentDocumentsAsync(dto);
          handlerStopwatch.Stop();
          handlerTime = handlerStopwatch.ElapsedMilliseconds / 1000.0;
          timings.Add($"RecentDocuments: {handlerTime:F1} seconds");
        }
        else if (dto.UserMessage.Equals("Finding documents with duplications", StringComparison.OrdinalIgnoreCase))
        {
          var handlerStopwatch = Stopwatch.StartNew();
          historyDto = await HandleDuplicatesAsync(dto);
          handlerStopwatch.Stop();
          handlerTime = handlerStopwatch.ElapsedMilliseconds / 1000.0;
          timings.Add($"Duplicates: {handlerTime:F1} seconds");
        }
        else
        {
          var handlerStopwatch = Stopwatch.StartNew();
          historyDto = await HandleGeneralQuestionAsync(dto);
          handlerStopwatch.Stop();
          handlerTime = handlerStopwatch.ElapsedMilliseconds / 1000.0;
          timings.Add($"Refine Question: {handlerTime:F1} seconds, ");
        }

        var llmStopwatch = Stopwatch.StartNew();
        string response = await _llmService.GetCompletionAsync(historyDto.Prompt, dto.ChatModel);
        llmStopwatch.Stop();
        double llmTime = llmStopwatch.ElapsedMilliseconds / 1000.0;
        timings.Add($"LLM Completion: {llmTime:F1} seconds");

        response = HtmlUtility.ConvertMarkdownToHtml(response.Trim());
        await _historyService.AddChatHistoryAsync(historyDto);

        totalStopwatch.Stop();
        double totalTime = totalStopwatch.ElapsedMilliseconds / 1000.0;


        string formattedResponse = $"{response}\n\nExecution Times:\n{string.Join("\n", timings)}";
        historyDto.Answer = formattedResponse;

        await _historyService.AddChatHistoryAsync(historyDto);

        return formattedResponse;
      }
      catch (HttpRequestException ex)
      {
        _logger.LogError(ex, "Network error while processing question: {RawMessage}", dto.UserMessage);
        throw new InvalidOperationException($"Failed to connect to chat service ({dto.ChatModel}).", ex);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Unexpected error while processing question: {RawMessage}", dto.UserMessage);
        throw new InvalidOperationException("An error occurred while processing the request.", ex);
      }
    }

    private async Task<ChatHistoryAddDTO> HandleSummarizeCollectionAsync(ChatDTO dto)
    {
      string summaries = await _pageService.GetContentSummariesAsync(dto.CollectionId);
      string chatPrompt = PromptUtility.BuildSummaryPrompt(summaries);

      return new ChatHistoryAddDTO
      {
        CollectionId = dto.CollectionId,
        Question = dto.UserMessage,
        Prompt = chatPrompt,
        TransformedQuestion = "",
        Answer = ""
      };
    }

    private async Task<ChatHistoryAddDTO> HandleRecentDocumentsAsync(ChatDTO dto)
    {
      // 使用内容摘要作为替代
      string recentDocs = await _pageService.GetContentSummariesAsync(dto.CollectionId);
      string chatPrompt = PromptUtility.BuildRecentDocumentsPrompt(recentDocs);

      return new ChatHistoryAddDTO
      {
        CollectionId = dto.CollectionId,
        Question = dto.UserMessage,
        Prompt = chatPrompt,
        TransformedQuestion = "",
        Answer = ""
      };
    }

    private async Task<ChatHistoryAddDTO> HandleDuplicatesAsync(ChatDTO dto)
    {
      string duplicates = await _vectorDbService.FindDuplicateDocumentsAsync(dto.CollectionId);
      string chatPrompt = PromptUtility.BuildDuplicatesPrompt(duplicates);

      return new ChatHistoryAddDTO
      {
        CollectionId = dto.CollectionId,
        Question = dto.UserMessage,
        Prompt = chatPrompt,
        TransformedQuestion = "",
        Answer = ""
      };
    }

    private async Task<ChatHistoryAddDTO> HandleGeneralQuestionAsync(ChatDTO dto)
    {
      string historyQuestion = GetHistoryQuestionFromContext(dto.Context);
      string prompt = PromptUtility.BuildTransformPrompt(dto.UserMessage, historyQuestion, ChatbotSettings.QueryOptions);
      string transformedQuestion = await _llmService.GetCompletionAsync(prompt, dto.ChatModel);

      var questions = PromptUtility.ExtractQuestions(transformedQuestion);

      if (!Convert.ToBoolean(questions[0]))
      {
        dto.Context = "";
      }

      questions.RemoveAt(0);
      string knowledgeBase = await _vectorDbService.ProcessAndSearchTextAsync(questions, dto.CollectionId, dto.EmbeddingModel, dto.ChatModel);

      string chatPrompt = PromptUtility.BuildChatPrompt(questions[0], knowledgeBase, dto.Context, dto.UserMessage);

      return new ChatHistoryAddDTO
      {
        CollectionId = dto.CollectionId,
        Question = dto.UserMessage,
        Prompt = chatPrompt,
        TransformedQuestion = questions[0],
        Answer = ""
      };
    }

    private string GetHistoryQuestionFromContext(string context)
		{
			if (string.IsNullOrWhiteSpace(context))
			{
				return "";
			}

			try
			{
				var contextItems = JsonSerializer.Deserialize<List<ContextItem>>(context);
				if (contextItems == null || !contextItems.Any())
				{
					return  "";
				}

				var userQuestions = contextItems
						.Where(item => item.Type == "user")
						.Select(item => item.Content.Trim())
						.Distinct()
						.ToList();

				if (!userQuestions.Any())
				{
					return "";
				}

				return userQuestions.LastOrDefault();
			}
			catch (JsonException ex)
			{
				_logger.LogWarning(ex, "Failed to parse context JSON, using rawQuestion only.");
				return "";
			}
		}
	}
}