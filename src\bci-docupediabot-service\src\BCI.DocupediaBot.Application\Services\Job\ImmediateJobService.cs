using BCI.DocupediaBot.Domain.Enums;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  public class ImmediateJobService
  {
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly ILogger<ImmediateJobService> _logger;

    public ImmediateJobService(ISchedulerFactory schedulerFactory, ILogger<ImmediateJobService> logger)
    {
      _schedulerFactory = schedulerFactory;
      _logger = logger;
    }

    public async Task SchedulePageEmbeddingAsync(Guid pageId, Guid collectionId, EmbeddingModel embeddingModel)
    {
      try
      {
        var scheduler = await _schedulerFactory.GetScheduler();

        var jobKey = new JobKey($"PageEmbedding_{pageId}_{collectionId}", "ImmediateJobs");

        // 如果已存在相同的任务，先删除
        if (await scheduler.CheckExists(jobKey))
        {
          await scheduler.DeleteJob(jobKey);
          _logger.LogInformation("Deleted existing embedding job for page {PageId}", pageId);
        }

        var jobDetail = JobBuilder.Create<PageEmbeddingJob>()
            .WithIdentity(jobKey)
            .UsingJobData("PageId", pageId)
            .UsingJobData("CollectionId", collectionId)
            .UsingJobData("EmbeddingModel", (int)embeddingModel)
            .Build();

        var trigger = TriggerBuilder.Create()
            .WithIdentity($"PageEmbeddingTrigger_{pageId}_{collectionId}", "ImmediateJobs")
            .StartNow()
            .Build();

        await scheduler.ScheduleJob(jobDetail, trigger);

        _logger.LogInformation("Scheduled immediate embedding job for page {PageId} in collection {CollectionId}", pageId, collectionId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to schedule embedding job for page {PageId}", pageId);
        throw;
      }
    }
  }
}
