using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Domain.Enums;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  public class ImmediateJobService
  {
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly ILogger<ImmediateJobService> _logger;

    public ImmediateJobService(ISchedulerFactory schedulerFactory, ILogger<ImmediateJobService> logger)
    {
      _schedulerFactory = schedulerFactory;
      _logger = logger;
    }

    public async Task<string> SchedulePageCreationAsync(PageAddDTO dto)
    {
      try
      {
        var scheduler = await _schedulerFactory.GetScheduler();

        // 确保调度器已启动
        if (!scheduler.IsStarted)
        {
          await scheduler.Start();
          _logger.LogInformation("Started Quartz scheduler");
        }

        var jobId = Guid.NewGuid().ToString();
        var jobKey = new JobKey($"PageCreation_{jobId}", "ImmediateJobs");

        var jobDetail = JobBuilder.Create<PageCreationJob>()
            .WithIdentity(jobKey)
            .UsingJobData("Url", dto.Url ?? string.Empty)
            .UsingJobData("UserToken", dto.UserToken ?? string.Empty)
            .UsingJobData("CollectionId", dto.CollectionId ?? string.Empty)
            .UsingJobData("IsIncludeChild", dto.IsIncludeChild)
            .UsingJobData("Title", dto.Title ?? string.Empty)
            .UsingJobData("SourceId", dto.SourceId ?? string.Empty)
            .Build();

        var trigger = TriggerBuilder.Create()
            .WithIdentity($"PageCreationTrigger_{jobId}", "ImmediateJobs")
            .StartNow()
            .Build();

        await scheduler.ScheduleJob(jobDetail, trigger);

        _logger.LogInformation("Scheduled immediate page creation job with ID: {JobId} for URL: {Url}", jobId, dto.Url);
        return jobId;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to schedule page creation job for URL: {Url}", dto.Url);
        throw;
      }
    }

    public async Task SchedulePageEmbeddingAsync(Guid pageId, Guid collectionId, EmbeddingModel embeddingModel)
    {
      try
      {
        var scheduler = await _schedulerFactory.GetScheduler();

        // 确保调度器已启动
        if (!scheduler.IsStarted)
        {
          await scheduler.Start();
          _logger.LogInformation("Started Quartz scheduler");
        }

        var jobKey = new JobKey($"PageEmbedding_{pageId}_{collectionId}", "ImmediateJobs");

        // 如果已存在相同的任务，先删除
        if (await scheduler.CheckExists(jobKey))
        {
          await scheduler.DeleteJob(jobKey);
          _logger.LogInformation("Deleted existing embedding job for page {PageId}", pageId);
        }

        var jobDetail = JobBuilder.Create<PageEmbeddingJob>()
            .WithIdentity(jobKey)
            .UsingJobData("PageId", pageId)
            .UsingJobData("CollectionId", collectionId)
            .UsingJobData("EmbeddingModel", (int)embeddingModel)
            .Build();

        var trigger = TriggerBuilder.Create()
            .WithIdentity($"PageEmbeddingTrigger_{pageId}_{collectionId}", "ImmediateJobs")
            .StartNow()
            .Build();

        await scheduler.ScheduleJob(jobDetail, trigger);

        _logger.LogInformation("Scheduled immediate embedding job for page {PageId} in collection {CollectionId}", pageId, collectionId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to schedule embedding job for page {PageId}", pageId);
        throw;
      }
    }

    public async Task<string> GetJobStatusAsync(string jobId)
    {
      try
      {
        var scheduler = await _schedulerFactory.GetScheduler();
        var jobKey = new JobKey($"PageCreation_{jobId}", "ImmediateJobs");

        if (await scheduler.CheckExists(jobKey))
        {
          var jobDetail = await scheduler.GetJobDetail(jobKey);
          var triggers = await scheduler.GetTriggersOfJob(jobKey);

          if (triggers.Any())
          {
            var trigger = triggers.First();
            var triggerState = await scheduler.GetTriggerState(trigger.Key);

            return triggerState switch
            {
              TriggerState.Normal => "Scheduled",
              TriggerState.Paused => "Paused",
              TriggerState.Complete => "Completed",
              TriggerState.Error => "Error",
              TriggerState.Blocked => "Running",
              _ => "Unknown"
            };
          }
        }

        return "NotFound";
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to get job status for job ID: {JobId}", jobId);
        return "Error";
      }
    }
  }
}
