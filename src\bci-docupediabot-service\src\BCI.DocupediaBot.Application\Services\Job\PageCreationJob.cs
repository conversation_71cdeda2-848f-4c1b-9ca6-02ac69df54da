using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Services.CollectPageContent;
using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using BCI.DocupediaBot.Application.Services.Page;

using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  [DisallowConcurrentExecution]
  public class PageCreationJob : IJob
  {
    private readonly IPageRepository _pageRepository;
    private readonly IMapper _mapper;
    private readonly ICollectPageContentService _collectPageContentService;
    private readonly IPagesInCollectionService _pagesInCollectionService;
    private readonly IConfluenceUrlService _confluenceUrlService;
    private readonly ICollectionService _collectionService;
    private readonly IPageService _pageService;
    private readonly ILogger<PageCreationJob> _logger;

    public PageCreationJob(
        IPageRepository pageRepository,
        IMapper mapper,
        ICollectPageContentService collectPageContentService,
        IPagesInCollectionService pagesInCollectionService,
        IConfluenceUrlService confluenceUrlService,
        ICollectionService collectionService,
        IPageService pageService,
        ILogger<PageCreationJob> logger)
    {
      _pageRepository = pageRepository ?? throw new ArgumentNullException(nameof(pageRepository));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _collectPageContentService = collectPageContentService ?? throw new ArgumentNullException(nameof(collectPageContentService));
      _pagesInCollectionService = pagesInCollectionService ?? throw new ArgumentNullException(nameof(pagesInCollectionService));
      _confluenceUrlService = confluenceUrlService ?? throw new ArgumentNullException(nameof(confluenceUrlService));
      _collectionService = collectionService ?? throw new ArgumentNullException(nameof(collectionService));
      _pageService = pageService ?? throw new ArgumentNullException(nameof(pageService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
      var jobData = context.JobDetail.JobDataMap;
      var url = jobData.GetString("Url");
      var userToken = jobData.GetString("UserToken");
      var collectionId = jobData.GetString("CollectionId");
      var isIncludeChild = jobData.GetBooleanValue("IsIncludeChild");
      var title = jobData.GetString("Title");
      var sourceId = jobData.GetString("SourceId");

      try
      {
        _logger.LogInformation("Starting page creation job for URL: {Url}", url);

        // 创建PageAddDTO
        var dto = new PageAddDTO
        {
          Url = url,
          UserToken = userToken,
          CollectionId = collectionId,
          IsIncludeChild = isIncludeChild,
          Title = title,
          SourceId = sourceId
        };

        string baseUrl = null;

        // 解析页面信息
        if (string.IsNullOrEmpty(dto.SourceId))
        {
          if (string.IsNullOrEmpty(dto.Url) || string.IsNullOrEmpty(dto.UserToken))
          {
            _logger.LogError("PageCreationJob requires either SourceId or URL with UserToken.");
            return;
          }

          try
          {
            _logger.LogInformation("Resolving PageInfo from URL: {Url}", dto.Url);
            var pageInfo = await _confluenceUrlService.ResolvePageInfoFromUrlAsync(dto.Url, dto.UserToken);
            dto.SourceId = pageInfo.PageId;
            dto.Title = pageInfo.Title;
            baseUrl = _confluenceUrlService.ExtractBaseUrlFromUrl(dto.Url);
            _logger.LogInformation("Resolved PageId: {SourceId}, Title: {Title}", dto.SourceId, dto.Title);
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "Failed to resolve PageInfo from URL: {Url}", dto.Url);
            return;
          }
        }

        // 检查页面是否已存在
        var allPages = await _pageRepository.GetAllAsync();
        var existingPage = allPages.FirstOrDefault(p => p.SourceId == dto.SourceId);
        PageResponseDTO pageDto;

        if (existingPage != null)
        {
          pageDto = _mapper.Map<PageResponseDTO>(existingPage);
          _logger.LogInformation("Page already exists with ID: {PageId}", pageDto.Id);
        }
        else
        {
          // 创建新页面
          var page = _mapper.Map<Domain.Entities.Page>(dto);
          await _pageRepository.CreateAsync(page);
          pageDto = _mapper.Map<PageResponseDTO>(page);
          _logger.LogInformation("Created new page with ID: {PageId}", pageDto.Id);

          // 收集页面内容
          try
          {
            if (string.IsNullOrEmpty(dto.UserToken) || string.IsNullOrEmpty(baseUrl))
            {
              _logger.LogError("Cannot collect page content: UserToken or BaseUrl is missing");
              return;
            }

            _logger.LogInformation("Starting content collection for page ID: {PageId}", pageDto.Id);
            await _collectPageContentService.CollectPageContentRecursiveAsync(pageDto, dto.UserToken, baseUrl);
            _logger.LogInformation("Completed content collection for page ID: {PageId}", pageDto.Id);
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "Failed to collect content for page ID: {PageId}", pageDto.Id);
            await _pageRepository.DeleteAsync(page);
            return;
          }
        }

        // 处理向量化
        if (!string.IsNullOrEmpty(dto.CollectionId) && Guid.TryParse(dto.CollectionId, out var collectionGuid))
        {
          try
          {
            var collection = await _collectionService.QueryCollectionWithPagesAsync(collectionGuid);
            if (collection != null)
            {
              _logger.LogInformation("Starting embedding for page ID: {PageId} in collection ID: {CollectionId}", pageDto.Id, collectionGuid);

              // 添加映射关系
              await _pagesInCollectionService.AddMappingAsync(collectionGuid, pageDto.Id);

              // 执行向量化
              var embedResult = await _pageService.EmbedContentAsync(pageDto, collectionGuid, collection.EmbeddingModel);
              if (embedResult.IsSuccess)
              {
                _logger.LogInformation("Successfully completed embedding for page ID: {PageId}", pageDto.Id);
              }
              else
              {
                _logger.LogError("Failed to embed page ID: {PageId}. Error: {Error}", pageDto.Id, embedResult.Msg);
              }
            }
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "Failed to process embedding for page ID: {PageId}", pageDto.Id);
          }
        }

        _logger.LogInformation("Successfully completed page creation job for page ID: {PageId}", pageDto.Id);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Exception during page creation job for URL: {Url}", url);
        throw;
      }
    }
  }
}
