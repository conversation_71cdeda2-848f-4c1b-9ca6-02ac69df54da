using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Services.CollectPageContent;
using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using BCI.DocupediaBot.Application.Services.Page;

using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  [DisallowConcurrentExecution]
  public class PageCreationJob : IJob
  {
    private readonly IPageRepository _pageRepository;
    private readonly IMapper _mapper;
    private readonly ICollectPageContentService _collectPageContentService;
    private readonly IPagesInCollectionService _pagesInCollectionService;
    private readonly IConfluenceUrlService _confluenceUrlService;
    private readonly ICollectionService _collectionService;
    private readonly IPageService _pageService;
    private readonly ILogger<PageCreationJob> _logger;

    public PageCreationJob(
        IPageRepository pageRepository,
        IMapper mapper,
        ICollectPageContentService collectPageContentService,
        IPagesInCollectionService pagesInCollectionService,
        IConfluenceUrlService confluenceUrlService,
        ICollectionService collectionService,
        IPageService pageService,
        ILogger<PageCreationJob> logger)
    {
      _pageRepository = pageRepository ?? throw new ArgumentNullException(nameof(pageRepository));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _collectPageContentService = collectPageContentService ?? throw new ArgumentNullException(nameof(collectPageContentService));
      _pagesInCollectionService = pagesInCollectionService ?? throw new ArgumentNullException(nameof(pagesInCollectionService));
      _confluenceUrlService = confluenceUrlService ?? throw new ArgumentNullException(nameof(confluenceUrlService));
      _collectionService = collectionService ?? throw new ArgumentNullException(nameof(collectionService));
      _pageService = pageService ?? throw new ArgumentNullException(nameof(pageService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
      var jobData = context.JobDetail.JobDataMap;
      var url = jobData.GetString("Url");
      var userToken = jobData.GetString("UserToken");
      var collectionId = jobData.GetString("CollectionId");
      var isIncludeChild = jobData.GetBooleanValue("IsIncludeChild");
      var title = jobData.GetString("Title");
      var sourceId = jobData.GetString("SourceId");

      var jobKey = context.JobDetail.Key.Name;
      var jobId = jobKey.Replace("PageCreation_", "");

      try
      {
        _logger.LogInformation("Starting page creation job {JobId} for URL: {Url}", jobId, url);
        ImmediateJobService.SetJobStatus(jobId, "Running");

        var dto = new PageAddDTO
        {
          Url = url,
          UserToken = userToken,
          CollectionId = collectionId,
          IsIncludeChild = isIncludeChild,
          Title = title,
          SourceId = sourceId
        };

        string baseUrl = null;

        if (string.IsNullOrEmpty(dto.SourceId))
        {
          if (string.IsNullOrEmpty(dto.Url) || string.IsNullOrEmpty(dto.UserToken))
          {
            _logger.LogError("PageCreationJob requires either SourceId or URL with UserToken.");
            return;
          }

          try
          {
            _logger.LogInformation("Resolving PageInfo from URL: {Url}", dto.Url);
            var pageInfo = await _confluenceUrlService.ResolvePageInfoFromUrlAsync(dto.Url, dto.UserToken);
            dto.SourceId = pageInfo.PageId;
            dto.Title = pageInfo.Title;
            baseUrl = _confluenceUrlService.ExtractBaseUrlFromUrl(dto.Url);
            _logger.LogInformation("Resolved PageId: {SourceId}, Title: {Title}", dto.SourceId, dto.Title);
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "Failed to resolve PageInfo from URL: {Url}", dto.Url);
            return;
          }
        }

        var allPages = await _pageRepository.GetAllAsync();
        var existingPage = allPages.FirstOrDefault(p => p.SourceId == dto.SourceId);
        PageResponseDTO pageDto;

        if (existingPage != null)
        {
          pageDto = _mapper.Map<PageResponseDTO>(existingPage);
          _logger.LogInformation("Page already exists with ID: {PageId}", pageDto.Id);
        }
        else
        {
          var page = _mapper.Map<Domain.Entities.Page>(dto);
          await _pageRepository.CreateAsync(page);
          pageDto = _mapper.Map<PageResponseDTO>(page);
          _logger.LogInformation("Created new page with ID: {PageId}", pageDto.Id);

          try
          {
            if (string.IsNullOrEmpty(dto.UserToken) || string.IsNullOrEmpty(baseUrl))
            {
              _logger.LogError("Cannot collect page content: UserToken or BaseUrl is missing");
              return;
            }

            _logger.LogInformation("Starting content collection for page ID: {PageId}", pageDto.Id);
            await _collectPageContentService.CollectPageContentRecursiveAsync(pageDto, dto.UserToken, baseUrl);
            _logger.LogInformation("Completed content collection for page ID: {PageId}", pageDto.Id);
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "Failed to collect content for page ID: {PageId}", pageDto.Id);
            await _pageRepository.DeleteAsync(page);
            return;
          }
        }

        if (!string.IsNullOrEmpty(dto.CollectionId) && Guid.TryParse(dto.CollectionId, out var collectionGuid))
        {
          try
          {
            var collection = await _collectionService.QueryCollectionWithPagesAsync(collectionGuid);
            if (collection != null)
            {
              await _pagesInCollectionService.AddMappingAsync(collectionGuid, pageDto.Id);

              _logger.LogInformation("Starting embedding process for page ID: {PageId}", pageDto.Id);
              var embedResult = await _pageService.EmbedContentAsync(pageDto, collectionGuid, collection.EmbeddingModel);
            }
            else
            {
              _logger.LogWarning("Collection not found for ID: {CollectionId}", collectionGuid);
            }
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "Failed to process embedding for page ID: {PageId}", pageDto.Id);
          }
        }
        else
        {
          _logger.LogInformation("No collection specified for page ID: {PageId}, skipping embedding", pageDto.Id);
        }

        _logger.LogInformation("Successfully completed page creation job {JobId} for page ID: {PageId}", jobId, pageDto.Id);
        ImmediateJobService.SetJobStatus(jobId, "Completed");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Exception during page creation job {JobId} for URL: {Url}", jobId, url);
        ImmediateJobService.SetJobStatus(jobId, "Error");
        throw;
      }
    }
  }
}
