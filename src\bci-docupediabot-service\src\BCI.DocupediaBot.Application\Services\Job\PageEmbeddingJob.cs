using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Domain.Enums;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  [DisallowConcurrentExecution]
  public class PageEmbeddingJob : IJob
  {
    private readonly IPageService _pageService;
    private readonly ILogger<PageEmbeddingJob> _logger;

    public PageEmbeddingJob(
        IPageService pageService,
        ILogger<PageEmbeddingJob> logger)
    {
      _pageService = pageService ?? throw new ArgumentNullException(nameof(pageService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
      var pageId = context.JobDetail.JobDataMap.GetGuidValue("PageId");
      var collectionId = context.JobDetail.JobDataMap.GetGuidValue("CollectionId");
      var embeddingModel = (EmbeddingModel)context.JobDetail.JobDataMap.Get("EmbeddingModel");

      try
      {
        _logger.LogInformation("Starting embedding process for page {PageId} in collection {CollectionId}", pageId, collectionId);
        
        var page = await _pageService.QueryPageById(pageId);
        if (page == null)
        {
          _logger.LogWarning("Page {PageId} not found for embedding", pageId);
          return;
        }

        var result = await _pageService.EmbedContentAsync(page, collectionId, embeddingModel);
        
        if (result.IsSuccess)
        {
          _logger.LogInformation("Successfully completed embedding for page {PageId}", pageId);
        }
        else
        {
          _logger.LogError("Failed to embed page {PageId}: {Error}", pageId, result.Msg);
        }
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Exception during embedding job for page {PageId}", pageId);
        throw;
      }
    }
  }
}
