﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Services.CollectPageContent;
using BCI.DocupediaBot.Application.Services.Content;
using BCI.DocupediaBot.Application.Services.VectorDb;
using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace BCI.DocupediaBot.Application.Services.Page
{
  public class PageService : IPageService
	{
		private readonly IPageRepository _pageRepository;
		private readonly IMapper _mapper;
		private readonly ICollectPageContentService _collectPageContentService;
		private readonly IPagesInCollectionService _pagesInCollectionService;
		private readonly IContentsInPageService _contentsInPageService;
		private readonly IVectorDbService _vectorDbService;
		private readonly IContentService _contentService;
		private readonly IConfluenceUrlService _confluenceUrlService;
		private readonly ICollectionService _collectionService;
		private readonly ILogger<PageService> _logger;

		public PageService(
				IPageRepository repository,
				IMapper mapper,
				ICollectPageContentService collectPageContentService,
				IPagesInCollectionService pagesInCollectionService,
				IVectorDbService vectorDbService,
				IContentService contentService,
				IContentsInPageService contentsInPageService,
				IConfluenceUrlService confluenceUrlService,
				ICollectionService collectionService,
				ILogger<PageService> logger)
		{
			_pageRepository = repository ?? throw new ArgumentNullException(nameof(repository));
			_mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
			_collectPageContentService = collectPageContentService ?? throw new ArgumentNullException(nameof(collectPageContentService));
			_pagesInCollectionService = pagesInCollectionService ?? throw new ArgumentNullException(nameof(pagesInCollectionService));
			_vectorDbService = vectorDbService ?? throw new ArgumentNullException(nameof(vectorDbService));
			_contentService = contentService ?? throw new ArgumentNullException(nameof(contentService));
			_contentsInPageService = contentsInPageService ?? throw new ArgumentNullException(nameof(contentsInPageService));
			_confluenceUrlService = confluenceUrlService ?? throw new ArgumentNullException(nameof(confluenceUrlService));
			_collectionService = collectionService ?? throw new ArgumentNullException(nameof(collectionService));
			_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		}

		public async Task<PageResponseDTO> AddPageAsync(PageAddDTO dto)
		{
			if (dto == null)
			{
				_logger.LogWarning("AddPageAsync received null DTO.");
				throw new ArgumentException("Page data cannot be null.");
			}
			string baseUrl = null;

      if (string.IsNullOrEmpty(dto.SourceId))
			{
				if (string.IsNullOrEmpty(dto.Url) || string.IsNullOrEmpty(dto.UserToken))
				{
					_logger.LogWarning("AddPageAsync requires either SourceId or URL with UserToken.");
					throw new ArgumentException("Either SourceId or URL with UserToken must be provided.");
				}

				try
				{
					_logger.LogInformation("Resolving PageInfo from URL: {Url}", dto.Url);
					var pageInfo = await _confluenceUrlService.ResolvePageInfoFromUrlAsync(dto.Url, dto.UserToken);
					dto.SourceId = pageInfo.PageId;
					dto.Title = pageInfo.Title;
					baseUrl = _confluenceUrlService.ExtractBaseUrlFromUrl(dto.Url);
					_logger.LogInformation("Resolved PageId: {SourceId}, Title: {Title} from URL: {Url}, BaseUrl: {BaseUrl}", dto.SourceId, dto.Title, dto.Url, baseUrl);
				}
				catch (Exception ex)
				{
					_logger.LogError(ex, "Failed to resolve PageInfo from URL: {Url}", dto.Url);
					throw new ArgumentException($"Failed to resolve page information from URL: {ex.Message}", ex);
				}
			}

			_logger.LogInformation("Adding page with SourceId: {SourceId}", dto.SourceId);

			var allPages = await _pageRepository.GetAllAsync();
			var existingPage = allPages.FirstOrDefault(p => p.SourceId == dto.SourceId);
			PageResponseDTO pageDto;

			if (existingPage != null)
			{
        pageDto = _mapper.Map<PageResponseDTO>(existingPage);
			}
			else
			{
				var page = _mapper.Map<Domain.Entities.Page>(dto);
				await _pageRepository.CreateAsync(page);
				pageDto = _mapper.Map<PageResponseDTO>(page);

        try
				{
					if (string.IsNullOrEmpty(dto.UserToken) || string.IsNullOrEmpty(baseUrl))
					{
						_logger.LogWarning("Cannot collect page content: UserToken or BaseUrl is missing");
						throw new ArgumentException("UserToken and BaseUrl are required for content collection");
					}
					await _collectPageContentService.CollectPageContentRecursiveAsync(pageDto, dto.UserToken, baseUrl);
        }
				catch (Exception ex)
				{
					_logger.LogError(ex, "Failed to collect content for new page ID: {PageId}", pageDto.Id);
					await _pageRepository.DeleteAsync(page);
					throw new InvalidOperationException($"Failed to collect page content: {ex.Message}", ex);
				}
			}

      // Embedding
			if (!string.IsNullOrEmpty(dto.CollectionId) && Guid.TryParse(dto.CollectionId, out var collectionGuid))
			{
				try
				{
					var collection = await _collectionService.QueryCollectionWithPagesAsync(collectionGuid);
					if (collection != null)
					{
						_logger.LogInformation("Auto-embedding content for newly added page ID: {PageId} in collection ID: {CollectionId}", pageDto.Id, collectionGuid);
						var embedResult = await EmbedContentAsync(pageDto, collectionGuid, collection.EmbeddingModel);
						if (!embedResult.IsSuccess)
						{
							_logger.LogWarning("Failed to auto-embed content for page ID: {PageId}. Error: {Error}", pageDto.Id, embedResult.Msg);
						}
						else
						{
							_logger.LogInformation("Successfully auto-embedded content for page ID: {PageId}", pageDto.Id);

              await _pagesInCollectionService.AddMappingAsync(collectionGuid, pageDto.Id);
              pageDto.IsEmbedding = true;
              _logger.LogInformation("Mapped page ID: {PageId} to collection ID: {CollectionId}", pageDto.Id, dto.CollectionId);
            }
					}
				}
				catch (Exception ex)
				{
					_logger.LogError(ex, "Failed to auto-embed content for page ID: {PageId}", pageDto.Id);
				}
			}

      var contentNumber = await GetContentNumber(pageDto.Id);
      pageDto.ContentNumber = contentNumber;

      return pageDto;
		}

		public async Task<ResponseResult> DeletePageByIdAsync(Guid pageId, Guid collectionId)
		{
			_logger.LogInformation("Deleting page mappings for page ID: {PageId} in collection ID: {CollectionId}", pageId, collectionId);

			var page = await _pageRepository.GetAsync(pageId);
			if (page == null)
			{
				_logger.LogWarning("Page not found for ID: {PageId}", pageId);
				return new ResponseResult { IsSuccess = false, Msg = "Page not found." };
			}

			var deleteResult = await _pagesInCollectionService.DeleteMappingAsync(pageId, collectionId);
			if (!deleteResult.IsSuccess)
			{
				_logger.LogWarning("Failed to delete mappings for page ID: {PageId}. Error: {Error}", pageId, deleteResult.Msg);
				return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete collection mappings: {deleteResult.Msg}" };
			}

      var contentIds = await _contentsInPageService.QueryContentIdsByPageIdAsync(pageId);

      foreach (var contentId in contentIds) {
        await _vectorDbService.DeletePointsByContentIdAsync(collectionId, contentId);
      }

      _logger.LogInformation("Page mappings deleted successfully for page ID: {PageId}", pageId);
			return new ResponseResult { IsSuccess = true, Msg = "Page mappings deleted successfully." };
		}

		public async Task<PageResponseDTO> QueryPageById(Guid pageId)
		{
			_logger.LogInformation("Querying page by ID: {PageId}", pageId);

			var page = await _pageRepository.GetAsync(pageId);
			if (page == null)
			{
				_logger.LogWarning("Page not found for ID: {PageId}", pageId);
				return null;
			}

			return _mapper.Map<PageResponseDTO>(page);
		}

		public async Task<List<PageResponseDTO>> QueryPagesByCollectionIdAsync(Guid collectionId)
		{
			_logger.LogInformation("Querying pages for collection ID: {CollectionId}", collectionId);

			var pageIds = await _pagesInCollectionService.QueryPageIdsByCollectionIdAsync(collectionId);
			if (pageIds == null || !pageIds.Any())
			{
				_logger.LogInformation("No pages found for collection ID: {CollectionId}", collectionId);
				return new List<PageResponseDTO>();
			}

			var allPages = await _pageRepository.GetAllAsync();
			var pageMappings = await _pagesInCollectionService.QueryMappingsByCollectionIdAsync(collectionId);
			var pages = allPages.Where(p => pageIds.Contains(p.Id)).ToList();


			var pageIdsList = pages.Select(p => p.Id).ToList();
			var contentNumbers = await GetContentNumbers(pageIdsList);


			var sourceIds = pages.Select(p => p.SourceId).ToList();
			var versionInfosDict = await _contentService.GetVersionInfosBySourceIdsAsync(sourceIds);
			var versionInfos = pages.ToDictionary(p => p.Id, p => versionInfosDict.GetValueOrDefault(p.SourceId, (null, null)));

			var pageDtos = pages.Select(p =>
			{
				var mapping = pageMappings.FirstOrDefault(m => m.PageId == p.Id && m.CollectionId == collectionId);
				var versionInfo = versionInfos[p.Id];
				return new PageResponseDTO
				{
					Id = p.Id,
					Title = p.Title,
					Url = p.Url,
					IsIncludeChild = p.IsIncludeChild,
					SourceId = p.SourceId,
					IsEmbedding = mapping?.IsEmbedding ?? false,
					ContentNumber = contentNumbers[p.Id],
					VersionNo = versionInfo.VersionNo,
					EmbeddingVersionNo = versionInfo.EmbeddingVersionNo,
					ModificationTime = p.ModificationTime
				};
			}).ToList();

			_logger.LogInformation("Retrieved {Count} pages for collection ID: {CollectionId}", pageDtos.Count, collectionId);
			return pageDtos;
		}

		public async Task<ResponseResult> UpdatePageByIdAsync(Guid pageId)
		{
			_logger.LogInformation("Updating page with ID: {PageId}", pageId);

			var page = await _pageRepository.GetAsync(pageId);
			if (page == null)
			{
				_logger.LogWarning("Page not found for ID: {PageId}", pageId);
				return new ResponseResult { IsSuccess = false, Msg = "Page not found." };
			}

			try
			{
				await _pageRepository.UpdateAsync(page);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to update page ID: {PageId}", pageId);
				return new ResponseResult { IsSuccess = false, Msg = $"Failed to update page: {ex.Message}" };
			}

			_logger.LogInformation("Page {PageId} updated successfully.", pageId);
			return new ResponseResult { IsSuccess = true, Msg = "Page updated successfully." };
		}

		public async Task<ResponseResult> EmbedContentAsync(PageResponseDTO dto, Guid collectionId, EmbeddingModel embeddingModel)
		{
			if (dto == null)
			{
				_logger.LogWarning("EmbedContentAsync received null DTO.");
				return new ResponseResult { IsSuccess = false, Msg = "Page data cannot be null." };
			}

			_logger.LogInformation("Embedding content for page ID: {PageId} in collection ID: {CollectionId}", dto.Id, collectionId);

			bool isEmbedding = await _pagesInCollectionService.GetEmbeddingAsync(dto.Id, collectionId);
			var contents = await _contentService.QueryContentsByPageIdAsync(dto.Id);

			var contentsToUpdate = new List<ContentUpdateDTO>();

			foreach (var content in contents)
			{
				if (isEmbedding && content.EmbeddingVersionNo != 0 && content.VersionNo == content.EmbeddingVersionNo)
				{
					_logger.LogDebug("Skipping content ID: {ContentId} as it is already embedded with matching version.", content.Id);
					continue;
				}

				try
				{
					await _vectorDbService.ProcessAndStoreTextAsync(content, collectionId, embeddingModel);
					var updateContentDTO = _mapper.Map<ContentUpdateDTO>(content);
					updateContentDTO.EmbeddingVersionNo = content.VersionNo;
					contentsToUpdate.Add(updateContentDTO);
				}
				catch (Exception ex)
				{
					_logger.LogError(ex, "Failed to embed content ID: {ContentId} for page ID: {PageId}", content.Id, dto.Id);
					return new ResponseResult { IsSuccess = false, Msg = $"Failed to embed content: {ex.Message}" };
				}
			}

			if (contentsToUpdate.Any())
			{
				await _contentService.UpdateContentsAsync(contentsToUpdate);
			}

			await _pagesInCollectionService.UpdateMappingAsync(dto.Id, collectionId, true);
			_logger.LogInformation("Content embedded successfully for page ID: {PageId}", dto.Id);
			return new ResponseResult { IsSuccess = true, Msg = "Page content embedded successfully." };
		}

		public async Task<ResponseResult> UpdateContentAsync(PageResponseDTO dto, Guid collectionId)
		{
			if (dto == null)
			{
				_logger.LogWarning("UpdateContentAsync received null DTO.");
				return new ResponseResult { IsSuccess = false, Msg = "Page data cannot be null." };
			}

			_logger.LogInformation("Updating content for page ID: {PageId} in collection ID: {CollectionId}", dto.Id, collectionId);

			var contents = await _contentService.QueryContentsByPageIdAsync(dto.Id);
			var contentsToUpdate = new List<ContentUpdateDTO>();

			foreach (var content in contents)
			{
				try
				{
					var result = await _vectorDbService.ProcessAndUpdateTextAsync(content, collectionId);
					if (result.IsSuccess)
					{
						if (result.Data != null)
						{
							contentsToUpdate.Add(result.Data as ContentUpdateDTO);
						}
					}
					else
					{
						_logger.LogWarning("Failed to process content ID: {ContentId}. Error: {Error}", content.Id, result.Msg);
					}
				}
				catch (Exception ex)
				{
					_logger.LogError(ex, "Failed to update content ID: {ContentId} for page ID: {PageId}", content.Id, dto.Id);
					return new ResponseResult { IsSuccess = false, Msg = $"Failed to update content: {ex.Message}" };
				}
			}

			if (contentsToUpdate.Any())
			{
				var updateResult = await _contentService.UpdateContentsAsync(contentsToUpdate);
				if (!updateResult.IsSuccess)
				{
					return updateResult;
				}
			}

			_logger.LogInformation("Content updated successfully for page ID: {PageId}", dto.Id);
			return new ResponseResult { IsSuccess = true, Msg = "Page content updated successfully." };
		}

		public async Task<ResponseResult> UpdateContentAndEmbedAsync(PageResponseDTO dto, Guid collectionId, EmbeddingModel embeddingModel)
		{
			if (dto == null)
			{
				_logger.LogWarning("UpdateContentAndEmbedAsync received null DTO.");
				return new ResponseResult { IsSuccess = false, Msg = "Page data cannot be null." };
			}

			_logger.LogInformation("Updating content and embedding for page ID: {PageId} in collection ID: {CollectionId}", dto.Id, collectionId);

			// First update content
			var updateResult = await UpdateContentAsync(dto, collectionId);
			if (!updateResult.IsSuccess)
			{
				_logger.LogError("Failed to update content for page ID: {PageId}. Error: {Error}", dto.Id, updateResult.Msg);
				return updateResult;
			}

			// Then embed content
			var embedResult = await EmbedContentAsync(dto, collectionId, embeddingModel);
			if (!embedResult.IsSuccess)
			{
				_logger.LogError("Failed to embed content for page ID: {PageId}. Error: {Error}", dto.Id, embedResult.Msg);
				return embedResult;
			}

			_logger.LogInformation("Content updated and embedded successfully for page ID: {PageId}", dto.Id);
			return new ResponseResult { IsSuccess = true, Msg = "Page content updated and embedded successfully." };
		}

		private async Task<int> GetContentNumber(Guid pageId)
		{
			var contentIds = await _contentsInPageService.QueryContentIdsByPageIdAsync(pageId);
			return contentIds?.Count ?? 0;
		}

		private async Task<Dictionary<Guid, int>> GetContentNumbers(List<Guid> pageIds)
		{
			if (pageIds == null || !pageIds.Any())
			{
				return new Dictionary<Guid, int>();
			}

			var result = new Dictionary<Guid, int>();


			var allMappings = await _contentsInPageService.QueryMappingsAsync();
			var relevantMappings = allMappings.Where(m => pageIds.Contains(m.PageId)).ToList();


			var mappingGroups = relevantMappings.GroupBy(m => m.PageId);

			foreach (var pageId in pageIds)
			{
				var count = mappingGroups.FirstOrDefault(g => g.Key == pageId)?.Count() ?? 0;
				result[pageId] = count;
			}

			return result;
		}

    public async Task<string> GetContentSummariesAsync(Guid collectionId)
    {
      var allPages = await QueryPagesByCollectionIdAsync(collectionId);
      if (allPages == null || !allPages.Any())
      {
        _logger.LogInformation("No pages found for collection ID: {CollectionId}", collectionId);
        return string.Empty;
      }
      var contentSummaries = "";
      foreach (var page in allPages)
      {
        var contents = await _contentService.QueryContentsByPageIdAsync(page.Id);
        var summaries = contents.Select(c => c.SummarizedContent).Where(s => !string.IsNullOrEmpty(s));
        contentSummaries += string.Join(";", summaries);
      }

      return contentSummaries;
    }

    public Task<string> GetRecentDocumentsAsync(Guid collectionId)
    {
      throw new NotImplementedException();
    }


  }
}