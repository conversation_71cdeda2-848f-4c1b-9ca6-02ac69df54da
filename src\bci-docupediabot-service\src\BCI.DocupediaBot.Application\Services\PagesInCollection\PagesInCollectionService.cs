﻿using AutoMapper;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Page
{
  public class PagesInCollectionService : IPagesInCollectionService
	{
		private readonly IPagesInCollectionRepository _repository;
    private readonly DocupediaBotDbContext _dbContext;

    public PagesInCollectionService(IPagesInCollectionRepository repository, IMapper mapper, DocupediaBotDbContext dbContext)
    {
      _repository = repository;
      _dbContext = dbContext;
    }


    public async Task<ResponseResult> DeleteMappingsByCollectionIdAsync(Guid collectionId)
		{
			var allRelations = await _repository.GetAllAsync();
			var relationsToDelete = allRelations.Where(r => r.CollectionId == collectionId).ToList();

			if (!relationsToDelete.Any())
				return new ResponseResult { IsSuccess = true, Msg = $"No pages found to delete for collection {collectionId}." };

			await _repository.DeleteRangeAsync(relationsToDelete);
			return new ResponseResult
			{
				IsSuccess = true,
				Msg = $"Deleted {relationsToDelete.Count} page relationships from collection {collectionId}."
			};
		}


		public async Task<ResponseResult> DeleteMappingAsync(Guid pageId, Guid collectionId)
		{
			var relation = await _repository.QueryAsync(r => r.PageId == pageId && r.CollectionId == collectionId);
			if (relation == null || !relation.Any())
				return new ResponseResult { IsSuccess = true, Msg = $"No page relationship found for page {pageId}." };

			await _repository.DeleteAsync(relation[0]);
			return new ResponseResult
			{
				IsSuccess = true,
				Msg = $"Deleted page relationship for page {pageId}."
			};
		}


		public async Task<List<Guid>> QueryPageIdsByCollectionIdAsync(Guid collectionId)
		{
			var allRelations = await _repository.GetAllAsync();
			return allRelations
					.Where(r => r.CollectionId == collectionId)
					.Select(r => r.PageId)
					.ToList();
		}


		public async Task AddMappingAsync(Guid collectionId, Guid pageId)
		{
			var allRelations = await _repository.GetAllAsync();
			var existingMapping = allRelations.FirstOrDefault(r => r.CollectionId == collectionId && r.PageId == pageId);

			if (existingMapping != null)
			{
				return;
			}

			var mapping = new PagesInCollection
			{
				CollectionId = collectionId,
				PageId = pageId,
				IsEmbedding = false
			};
			await _repository.CreateAsync(mapping);
		}

    public async Task<ResponseResult> UpdateMappingAsync(Guid pageId, Guid collectionId, bool isEmbedding)
    {
      try
      {
        // 使用EF Core的方式更新，避免直接SQL
        var mapping = await _repository.GetAllAsync();
        var existingMapping = mapping.FirstOrDefault(m => m.PageId == pageId && m.CollectionId == collectionId);

        if (existingMapping == null)
        {
          return new ResponseResult { IsSuccess = false, Msg = $"No page relationship found for page {pageId} in collection {collectionId}." };
        }

        existingMapping.IsEmbedding = isEmbedding;
        await _repository.UpdateAsync(existingMapping);

        return new ResponseResult
        {
          IsSuccess = true,
          Msg = $"Updated page relationship for page {pageId} in collection {collectionId}."
        };
      }
      catch (Exception ex)
      {
        return new ResponseResult { IsSuccess = false, Msg = ex.Message };
      }
    }

    public async Task<bool> GetEmbeddingAsync(Guid pageId, Guid collectionId)
		{
			var relation = await _repository.QueryAsync(r => r.PageId == pageId && r.CollectionId == collectionId);
			if (relation == null || !relation.Any())
			{
				return false;
			}
			var existingRelation = relation.First();
			return existingRelation.IsEmbedding;
		}

		public async Task<List<PagesInCollection>> QueryMappingsByCollectionIdAsync(Guid collectionId)
		{
			var allRelations = await _repository.GetAllAsync();
			return allRelations
					.Where(r => r.CollectionId == collectionId)
					.ToList();
		}
  }
}