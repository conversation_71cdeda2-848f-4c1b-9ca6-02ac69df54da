﻿using BCI.DocupediaBot.Application.Services.Job;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Configuration;
using BCI.DocupediaBot.Infrastructure.Identity;
using Bosch.Foundation.ErrorHandling;
using Bosch.Foundation.Health;
using Bosch.Foundation.Logging.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Bosch.Foundation.Security.Api;
using Grpc.Net.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using Quartz;
using Serilog;
using Serilog.Events;
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace BCI.DocupediaBot.UIService
{
  public class Program
  {
    public static void Main(string[] args)
    {
      var builder = WebApplication.CreateBuilder(args);
      builder.Configuration.AddJsonFile("chatbotsettings.json", optional: false, reloadOnChange: true);
      ChatbotSettings.Initialize(builder.Configuration);
      ConfigureLogging(builder);
      ConfigureServices(builder);
      var app = builder.Build();
      ConfigureMiddleware(app);
      app.Run();
    }

    private static void ConfigureLogging(WebApplicationBuilder builder)
    {
      var configuration = builder.Configuration;

      Log.Logger = new LoggerConfiguration()
          .ReadFrom.Configuration(configuration)
          .WriteTo.Logger(lc => lc
              .Filter.ByIncludingOnly(evt => evt.Level == LogEventLevel.Information)
              .WriteTo.File(
                  path: Path.Combine(configuration["Serilog:LogDirectory"] ?? "log", "information/info-.log"),
                  rollingInterval: RollingInterval.Day,
                  outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {Message:lj}{NewLine}{Exception}"))
          .WriteTo.Logger(lc => lc
              .Filter.ByIncludingOnly(evt => evt.Level == LogEventLevel.Warning)
              .WriteTo.File(
                  path: Path.Combine(configuration["Serilog:LogDirectory"] ?? "log", "warning/warn-.log"),
                  rollingInterval: RollingInterval.Day,
                  outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {Message:lj}{NewLine}{Exception}"))
          .WriteTo.Logger(lc => lc
              .Filter.ByIncludingOnly(evt => evt.Level == LogEventLevel.Error)
              .WriteTo.File(
                  path: Path.Combine(configuration["Serilog:LogDirectory"] ?? "log", "error/error-.log"),
                  rollingInterval: RollingInterval.Day,
                  outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {Message:lj}{NewLine}{Exception}"))
          .CreateLogger();

      builder.Host.UseSerilog();
    }

    private static void ConfigureServices(WebApplicationBuilder builder)
    {
      var config = builder.Configuration;
      var services = builder.Services;

      ConfigureSettings(services, config);
      ConfigureGrpc(services);
      ConfigureHttpClients(services);
      ConfigureFrameworkServices(services, config);
      ConfigureAppServices(services, config);
      ConfigureAutoMapper(services);
      ConfigureQuartzServices(services);
    }

    private static void ConfigureQuartzServices(IServiceCollection services)
    {
      services.AddQuartz(q =>
      {
        q.UseMicrosoftDependencyInjectionJobFactory();
        q.UseSimpleTypeLoader();
        q.UseInMemoryStore();
      });

      services.AddQuartzHostedService(options =>
      {
        options.WaitForJobsToComplete = true;
      });

      services.AddSingleton<IJobConfigService, JobConfigService>();
      services.AddSingleton<JobSchedulerService>();
      services.AddSingleton<ImmediateJobService>();
      services.AddHostedService<JobInitializerService>();
      services.AddTransient<UpdateCollectionJob>();
      services.AddTransient<PageEmbeddingJob>();
      services.AddTransient<PageCreationJob>();
    }

    private static void ConfigureSettings(IServiceCollection services, IConfiguration config)
    {
      services.Configure<OidcConfiguration>(config.GetSection("OIDC"));
      services.Configure<JwtTokenOptions>(config.GetSection("JwtTokenOptions"));
    }

    private static void ConfigureGrpc(IServiceCollection services)
    {
      var activeVectorDB = ChatbotSettings.VectorDB.Instances[ChatbotSettings.VectorDB.Active];
      var grpcChannelOptions = new GrpcChannelOptions
      {
        HttpHandler = new SocketsHttpHandler { UseProxy = false }
      };
      var channel = GrpcChannel.ForAddress(activeVectorDB.GrpcAddress, grpcChannelOptions);
      var grpcClient = new QdrantGrpcClient(channel);
      var client = new QdrantClient(grpcClient);
      services.AddTransient(sp => client);
    }

    private static void ConfigureHttpClients(IServiceCollection services)
    {
      services.AddHttpClient("ConfluenceClient", httpClient =>
      {
      }).ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler { UseProxy = false });

      var ollamaProvider = ChatbotSettings.GetModel<ModelProviderSettings>("Ollama");
      if (ollamaProvider != null)
      {
        services.AddHttpClient("OllamaClient", httpClient =>
        {
          httpClient.BaseAddress = new Uri(ollamaProvider.Endpoint);
          httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
        }).ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler { UseProxy = false });
      }

      var azureProvider = ChatbotSettings.GetModel<ModelProviderSettings>("azure");
      if (azureProvider != null)
      {
        services.AddHttpClient("AzureClient", httpClient =>
        {
          httpClient.BaseAddress = new Uri(azureProvider.Endpoint);
        }).ConfigurePrimaryHttpMessageHandler(() =>
        {
          var proxy = new WebProxy(ChatbotSettings.Proxy.Url, true)
          {
            Credentials = new NetworkCredential(ChatbotSettings.Proxy.Username, ChatbotSettings.Proxy.Password)
          };
          return new HttpClientHandler
          {
            Proxy = proxy,
            UseProxy = true
          };
        });
      }
    }

    private static void ConfigureFrameworkServices(IServiceCollection services, IConfiguration config)
    {
      services.AddHttpContextAccessor();
      services.AddScoped<ICurrentUserAccessor, CurrentUserAccessor>();
      services.AddHttpClient();
      services.AddMVC().AddValidationResponseHandler();
      services.AddCors();
      services.AddExceptionHandlerContainer();

      services.AddApiVersions();
      services.AddDistributedMemoryCache();
      services.AddLogging();
      services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
          options.RequireHttpsMetadata = false;
          options.SaveToken = true;
          var token = config.GetSection("JwtTokenOptions").Get<JwtTokenOptions>();
          options.TokenValidationParameters = new TokenValidationParameters
          {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(token.Secret)),
            ValidIssuer = token.Issuer,
            ValidateIssuer = true,
            ValidateAudience = false,
            ClockSkew = TimeSpan.Zero
          };
        });
      services.AddControllers();
      services.AddEndpointsApiExplorer();
      services.AddSwaggerGenWithAuthorization();
      services.AddDefaultSecurityHeaders();
    }

    private static void ConfigureAppServices(IServiceCollection services, IConfiguration config)
    {
      services.AddFoundationHealth(config);
      services.AddAppServices();
      services.AddDomainServices();
      services.AddEFRepositories();
      services.AddDatabase(config);
    }

    private static void ConfigureAutoMapper(IServiceCollection services)
    {
      var assemblies = AppDomain.CurrentDomain.GetAssemblies()
              .Where(x => x.FullName?.Contains("BCI.DocupediaBot") ?? false);
      services.AddAutoMapper(assemblies);
    }

    private static void ConfigureMiddleware(WebApplication app)
    {
      ConfigureDevelopmentMiddleware(app);
      ConfigureCommonMiddleware(app);
      ConfigureRouting(app);
      ConfigureSpa(app);
    }

    private static void ConfigureDevelopmentMiddleware(WebApplication app)
    {
      if (!app.Environment.IsProduction())
      {

      }
      app.UseTheSwagger();
    }

    private static void ConfigureCommonMiddleware(WebApplication app)
    {
      app.UseLifeCycleLogging();
      app.UseCors(options =>
      {
        options.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod().WithExposedHeaders("token", "refresh_token");
      });
      app.UseErrorHandler();
      app.UseApiVersioning();
      app.UseRouting();
    }

    private static void ConfigureRouting(WebApplication app)
    {
      app.UseAuthorization();
      app.UseFoundationHealthEndpoint();
      app.UseStaticFiles();
      app.UseEndpoints(endpoints =>
      {
        var builder = endpoints.MapControllers();
        if (app.Configuration.IfWithoutNIAS())
        {
          builder.AllowAnonymous();
        }
      });
    }

    private static void ConfigureSpa(WebApplication app)
    {
      app.UseSpa(spa =>
      {
        spa.Options.SourcePath = "wwwroot";
        if (app.Environment.IsDevelopment())
        {
          spa.UseProxyToSpaDevelopmentServer(ChatbotSettings.SpaDevServerUrl);
        }
      });
    }
  }
}