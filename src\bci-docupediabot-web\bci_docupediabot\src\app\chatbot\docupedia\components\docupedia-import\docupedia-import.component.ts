import { DOCUMENT } from '@angular/common';
import { Component, Inject, OnInit, OnDestroy, ViewContainerRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ModalWindowService, NotificationService, AlertService, AlertBoxOptions, AlertType, PrimaryButton } from '@bci-web-core/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { DocupediaImportAddcollectionDialogComponent } from '../../dialogs/docupedia-import-add-collection-dialog/docupedia-import-add-collection-dialog.component';
import { DocupediaImportAddUrlDialogComponent } from '../../dialogs/docupedia-import-add-url-dialog/docupedia-import-add-url-dialog.component';
import { TokenSetupGuideDialogComponent } from '../../dialogs/token-setup-guide-dialog/token-setup-guide-dialog.component';
import { CollectionService } from '@shared/services/docupedia/collection.service';
import { Collection, Page } from '@shared/models/docupedia.model';
import { PageService } from '@shared/services/docupedia/page.service';
import { PageEvent } from '@bci-web-core/web-components/dist/types/components/paginator/paginator.interface';
import { PermissionService } from '@shared/services/permission.service';
import { AuthService } from '@shared/services/auth.service';
import { SysUserResponseDTO } from '@shared/models/system.model';

@Component({
  selector: 'app-docupedia-import',
  templateUrl: './docupedia-import.component.html',
  styleUrls: ['./docupedia-import.component.scss']
})
export class DocupediaImportComponent implements OnInit, OnDestroy {
  useSlideoverSidebarMode = false;
  displayedColumns = ['title', 'isIncludeChild', 'isEmbedding', 'contentNumber', 'modificationTime', 'actions'];
  message =
    'Appeared the first time with last major release 4.0. We are in urgent need of a working license to complete our tasks within the schedules.';

  collections: Collection[] = [];

  searchTerm: string = '';
  pageNumber: number = 0;
  pageSize: number = 10;
  itemCount: number = 0;
  pageSizeOptions: number[] = [3, 5, 10, 20];
  private searchSubject = new Subject<string>();
  private subscription = new Subscription();

  constructor(
    public dialog: MatDialog,
    private _matDialog: MatDialog,
    private router: Router,
    private collectionService: CollectionService,
    private pageService: PageService,
    private notificationService: NotificationService,
    @Inject(DOCUMENT) doc: any,
    private _modalWindowService: ModalWindowService,
    private alertService: AlertService,
    private viewContainerRef: ViewContainerRef,
    private permissionService: PermissionService,
    private authService: AuthService
  ) {
    dialog.afterOpened.subscribe(() => {
      if (!doc.body.classList.contains('no-scroll')) {
        doc.body.classList.add('no-scroll');
      }
    });
    dialog.afterAllClosed.subscribe(() => {
      doc.body.classList.remove('no-scroll');
    });
  }

  get canCreateCollectionAndPage(): boolean {
    return this.permissionService.canCreateCollectionAndPage();
  }

  canCRUDKnowledge(collection: Collection): boolean {
    const currentUser = this.authService.getCurrentUser();
    if (this.permissionService.canAccessSystemMenu())
    {
      return true;
    }
    if (!currentUser) {
      return false;
    }
    return collection.creator === currentUser.userNTAccount;
  }

  ngOnInit(): void {
    this.subscription.add(this.searchSubject.pipe(debounceTime(600)).subscribe((searchTerm) => {
      this.searchTerm = searchTerm;
      this.pageNumber = 0;
      this.loadCollections();
    }));

    this.loadCollections();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  loadCollections(): void {
    this.collectionService.getCollections().subscribe({
      next: (data: Collection[]) => {
        let filteredData = data;
        if (this.searchTerm) {
          const term = this.searchTerm.toLowerCase();
          filteredData = data.filter(collection =>
            collection.name.toLowerCase().includes(term) ||
            collection.comment.toLowerCase().includes(term) ||
            collection.embeddingModel.toString().toLowerCase().includes(term) ||
            (collection.status === 1 ? 'active' : 'inactive').includes(term) ||
            (collection.isEmbedding ? 'embedded' : 'not embedded').includes(term) ||
            (collection.isAutomaticUpdate ? 'automatic' : 'manual').includes(term)
          );
        }

        this.itemCount = filteredData.length;
        const startIndex = this.pageNumber * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        this.collections = filteredData.slice(startIndex, endIndex).map(collection => ({
          ...collection,
          isExpend: false
        }));
      },
      error: (error) => this.handleError(error, 'Failed to load collections')
    });
  }

  addCollection(): void {
    const dialogRef = this.dialog.open(DocupediaImportAddcollectionDialogComponent, {
      data: { mode: 'add' },
      height: '650px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.collectionService.addCollection(result).subscribe({
          next: () => {
            this.handleSuccess('Collection added successfully');
            this.loadCollections();
          },
          error: (error) => this.handleError(error, 'Failed to add collection')
        });
      }
    });
  }

  editCollection(collection: Collection): void {
    this._modalWindowService
      .openDialogWithComponent(DocupediaImportAddcollectionDialogComponent, {
        data: { mode: 'edit', collection },
        height: '650px'
      }, this._matDialog)
      .afterClosed()
      .subscribe((result: Collection | undefined) => {
        if (result && collection.id) {
          this.collectionService.updateCollection(collection.id, result).subscribe({
            next: () => {
              this.handleSuccess('Collection updated successfully');
              this.loadCollections();
            },
            error: (error) => this.handleError(error, 'Failed to update collection')
          });
        }
      });
  }

  deleteCollection(collection: Collection): void {
    if (!collection.id) {
      this.notificationService.warning('No collection ID provided');
      return;
    }

    const alertBoxOptions: AlertBoxOptions = {
      title: 'Delete Collection',
      description: `Are you sure you want to delete collection "${collection.name}"?`,
      infoText: 'This action cannot be undone.',
      type: AlertType.Warning,
      primaryButton: PrimaryButton.RightButton,
      leftButtonTitle: 'Delete',
      rightButtonTitle: 'Cancel',
      outerLeftButtonDisabled: false,
      leftButtonDisabled: false,
      rightButtonDisabled: false,
    };

    const ref = this.alertService.openAlertBox(alertBoxOptions, this.viewContainerRef, '500px');
    ref.afterClosed().subscribe((res) => {
      if (res === "left") {
        this.collectionService.deleteCollection(collection.id).subscribe({
          next: () => {
            this.handleSuccess('Collection deleted successfully');
            this.loadCollections();
          },
          error: (error) => this.handleError(error, 'Failed to delete collection')
        });
      }
    });
  }

  deletePage(collection: Collection, page: Page): void {
    if (!page.id || !collection.id) {
      this.notificationService.warning('Missing page or collection ID');
      return;
    }

    const alertBoxOptions: AlertBoxOptions = {
      title: 'Delete Page',
      description: `Are you sure you want to delete page "${page.title}"?`,
      infoText: 'This action cannot be undone.',
      type: AlertType.Warning,
      primaryButton: PrimaryButton.RightButton,
      leftButtonTitle: 'Delete',
      rightButtonTitle: 'Cancel',
      outerLeftButtonDisabled: false,
      leftButtonDisabled: false,
      rightButtonDisabled: false,
    };

    const ref = this.alertService.openAlertBox(alertBoxOptions, this.viewContainerRef, '500px');
    ref.afterClosed().subscribe((res) => {
      if (res === "left") {
        this.pageService.deletePage(page.id!, collection.id).subscribe({
          next: () => {
            this.handleSuccess('Page deleted successfully');
            this.loadCollections();
          },
          error: (error) => this.handleError(error, 'Failed to delete page')
        });
      }
    });
  }

  updateAndEmbedPageContents(page: Page, collection: Collection): void {
    if (!page.id || !collection.id || !collection.embeddingModel) {
      this.notificationService.warning('Missing page, collection ID or embedding model');
      return;
    }
    this.pageService.updateAndEmbedPageContents(collection.id, page.id, collection.embeddingModel).subscribe({
      next: () => {
        this.handleSuccess('Page contents updated and embedded successfully');
        this.loadCollections();
      },
      error: (error) => this.handleError(error, 'Failed to update and embed page contents')
    });
  }

  updateAndEmbedCollectionContents(collection: Collection): void {
    if (!collection.id || !collection.embeddingModel) {
      this.notificationService.warning('No collection ID or embedding model provided');
      return;
    }
    this.collectionService.updateAndEmbedCollectionContents(collection.id, collection.embeddingModel).subscribe({
      next: () => {
        this.handleSuccess('Collection contents updated and embedded successfully');
        this.loadCollections();
      },
      error: (error) => this.handleError(error, 'Failed to update and embed collection contents')
    });
  }

  // Keep original methods for backward compatibility if needed elsewhere
  updatePageContents(page: Page, collection: Collection): void {
    if (!page.id || !collection.id) {
      this.notificationService.warning('Missing page or collection ID');
      return;
    }
    this.pageService.updatePageContents(collection.id, page.id).subscribe({
      next: () => {
        this.handleSuccess('Page contents updated successfully');
        this.loadCollections();
      },
      error: (error) => this.handleError(error, 'Failed to update page contents')
    });
  }

  updateCollectionContents(collection: Collection): void {
    if (!collection.id) {
      this.notificationService.warning('No collection ID provided');
      return;
    }
    this.collectionService.updateCollectionContents(collection.id).subscribe({
      next: () => {
        this.handleSuccess('Collection contents updated successfully');
        this.loadCollections();
      },
      error: (error) => this.handleError(error, 'Failed to update collection contents')
    });
  }

  embeddingPageContents(page: Page, collection: Collection): void {
    if (!page.id || !collection.id || !collection.embeddingModel) {
      this.notificationService.warning('Missing page or collection ID');
      return;
    }
    this.pageService.embeddingPageContents(collection.id, page.id, collection.embeddingModel).subscribe({
      next: () => {
        this.handleSuccess('Page contents embedded successfully');
        this.loadCollections();
      },
      error: (error) => this.handleError(error, 'Failed to embed page contents')
    });
  }

  embeddingCollectionContents(collection: Collection): void {
    if (!collection.id || !collection.embeddingModel) {
      this.notificationService.warning('No collection ID provided');
      return;
    }
    this.collectionService.embeddingCollectionContents(collection.id, collection.embeddingModel).subscribe({
      next: () => {
        this.handleSuccess('Collection contents embedded successfully');
        this.loadCollections();
      },
      error: (error) => this.handleError(error, 'Failed to embed collection contents')
    });
  }

  expend(collection: Collection): void {
    collection.isExpend = !collection.isExpend;
    if (collection.isExpend) {
      this.pageService.getPages(collection.id).subscribe({
        next: (pages: Page[]) => {
          collection.pages = pages;
        },
        error: (error) => this.handleError(error, 'Failed to load pages')
      });
    }
  }

  addPage(collection: Collection): void {
    if (!this.validateUserToken()) {
      this.showTokenSetupGuide();
      return;
    }

    this._modalWindowService
      .openDialogWithComponent(DocupediaImportAddUrlDialogComponent, {
        width: '800px',
        height: "400px",
        data: { collectionId: collection.id }
      }, this._matDialog)
      .afterClosed()
      .subscribe((result: Page | undefined) => {
        if (result) {
          this.pageService.addPage(result).subscribe({
            next: (response: Page) => {
              this.handleSuccess('Page added successfully. Embedding is processing in background...');
              collection.pages = [...(collection.pages || []), response];

              // 开始轮询向量化状态
              this.pollEmbeddingStatus(response, collection);
            },
            error: (error) => this.handleError(error, 'Failed to add page')
          });
        }
      });
  }

  private pollEmbeddingStatus(page: Page, collection: Collection): void {
    if (!page.id || !collection.id) return;

    const pollInterval = setInterval(() => {
      this.pageService.getEmbeddingStatus(page.id!, collection.id!).subscribe({
        next: (status: any) => {
          if (status.isEmbedding && status.isUpToDate) {
            clearInterval(pollInterval);
            this.handleSuccess(`Page "${page.title}" embedding completed successfully!`);
            this.loadCollections(); // 刷新页面状态
          }
        },
        error: (error) => {
          console.error('Error polling embedding status:', error);
          clearInterval(pollInterval);
        }
      });
    }, 3000); // 每3秒轮询一次

    // 设置最大轮询时间（5分钟）
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 300000);
  }

  private validateUserToken(): boolean {
    try {
      const userInfo = localStorage.getItem('user_info');
      if (!userInfo) {
        return false;
      }

      const user = JSON.parse(userInfo);
      const token = user?.docupediaToken;

      if (!token || token.trim() === '') {
        return false;
      }

      if (token.length < 10) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating user token:', error);
      return false;
    }
  }

  private showTokenSetupGuide(): void {
    const dialogRef = this._matDialog.open(TokenSetupGuideDialogComponent, {
      width: '700px',
      maxHeight: '80vh',
      disableClose: false
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'profile') {
        this.router.navigate(['/user-profile']);
      } else if (result === 'saved') {
        this.handleSuccess('Token saved successfully! You can now add pages from URLs.');
      }
    });
  }

  onSearchChange(searchTerm: string): void {
    this.searchSubject.next(searchTerm);
  }

  onPageChange(pageEvent: PageEvent): void {
    if (pageEvent.pageIndex === this.pageNumber && pageEvent.pageSize === this.pageSize) {
      return;
    }
    this.pageNumber = pageEvent.pageIndex;
    this.pageSize = pageEvent.pageSize;
    this.loadCollections();
  }

  private handleSuccess(message: string): void {
    this.notificationService.success(message);
  }

  private handleError(error: any, defaultMessage: string = 'An error occurred'): void {
    const errorMessage = error.message ?? defaultMessage;
    this.notificationService.error(errorMessage);
  }
}
