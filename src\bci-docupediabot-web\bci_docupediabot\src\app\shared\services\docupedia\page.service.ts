import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { ApiResponse, Page } from '@shared/models/docupedia.model';
import { ResponseResult } from '@shared/models/share.model';
import { environment } from 'src/environments/environment';
import { EmbeddingModel } from '@shared/enums';


@Injectable({
  providedIn: 'root'
})
export class PageService {
  private apiUrl = `${environment.baseUrl}/api/pages`;

  constructor(private http: HttpClient) {}

  addPage(page: Page): Observable<Page> {
    return this.http.post<ApiResponse<Page>>(`${this.apiUrl}`, page).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  addPageAsync(page: Page): Observable<string> {
    return this.http.post<ApiResponse<string>>(`${this.apiUrl}/async`, page).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  getJobStatus(jobId: string): Observable<any> {
    return this.http.get<ApiResponse<any>>(`${this.apiUrl}/job/${jobId}/status`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  cancelJob(jobId: string): Observable<boolean> {
    return this.http.delete<ApiResponse<boolean>>(`${this.apiUrl}/job/${jobId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  deletePage(pageId: string, collectionId: string): Observable<ResponseResult> {
    return this.http.delete<ApiResponse<ResponseResult>>(`${this.apiUrl}/${pageId}/${collectionId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  getPages(collectionId: string): Observable<Page[]> {
    return this.http.get<ApiResponse<Page[]>>(`${this.apiUrl}/collection/${collectionId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  getPage(pageId: string): Observable<Page> {
    return this.http.get<ApiResponse<Page>>(`${this.apiUrl}/${pageId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updatePage(pageId: string, page: Partial<Page>): Observable<ResponseResult> {
    return this.http.put<ApiResponse<ResponseResult>>(`${this.apiUrl}/${pageId}`, page).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updatePageContents(collectionId: string, pageId?: string): Observable<ResponseResult> {
    return this.http.post<ApiResponse<ResponseResult>>(`${this.apiUrl}/${pageId}/contents/update/${collectionId}`, null).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  embeddingPageContents(collectionId: string, pageId?: string, embeddingModel?: EmbeddingModel): Observable<ResponseResult> {
    const url = `${this.apiUrl}/${pageId}/contents/embed/${collectionId}`;
    return this.http.post<ApiResponse<ResponseResult>>(url, { embeddingModel }).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updateAndEmbedPageContents(collectionId: string, pageId?: string, embeddingModel?: EmbeddingModel): Observable<ResponseResult> {
    const url = `${this.apiUrl}/${pageId}/contents/update-and-embed/${collectionId}`;
    return this.http.post<ApiResponse<ResponseResult>>(url, { embeddingModel }).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }
}
